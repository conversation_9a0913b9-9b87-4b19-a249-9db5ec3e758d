{"model_type": "<PERSON><PERSON><PERSON><PERSON>", "n_clusters": 4, "features_used": ["recency", "frequency", "monetary", "customer_lifespan_days", "days_since_first_order", "recency_days", "total_amount", "amount_std", "avg_order_value", "amount_cv", "amount_total", "avg_amount", "amount_std_dev", "min_amount", "max_amount", "amount_cv_coef", "amount_range", "order_value_mean", "total_orders", "purchase_frequency", "order_count", "mon<PERSON>_moyen"], "n_samples": 99441, "silhouette_score": 0.4168307800681028, "calinski_harabasz_score": 70804.34230326499, "inertia": 412202.9478923217, "random_state": 42, "training_date": "2025-06-03 19:36:36", "cluster_distribution": {"0": 43262, "1": 49170, "2": 760, "3": 6249}, "optimization_results": {"k_range_tested": [2, 3, 4, 5, 6, 7, 8, 9, 10], "optimal_k_silhouette": 2, "optimal_k_calinski": 10, "justification": "Consensus entre les différentes métriques"}}