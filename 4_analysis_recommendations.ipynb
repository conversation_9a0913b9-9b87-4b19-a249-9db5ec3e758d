{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 4 : Analyse des segments & Recommandations marketing\n", "\n", "## Objectif\n", "Analyser les segments obtenus via le clustering, identifier des profils clients clairs et formuler des recommandations concrètes et personnalisées pour chaque groupe.\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données clusterisées\n", "\n", "### 1.1 Import des librairies d'analyse marketing"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dossier assuré : /Users/<USER>/Developer/OPC/P5/data/raw\n", "Dossier assuré : /Users/<USER>/Developer/OPC/P5/data/clean\n", "Dossier assuré : /Users/<USER>/Developer/OPC/P5/reports\n", "Le dossier source /Users/<USER>/Developer/OPC/P5/data/clean/features n'existe pas, rien à déplacer.\n", "==================================================\n", "\u001b[1m\u001b[95m🚀 INITIALISATION DU NOTEBOOK\u001b[0m\n", "==================================================\n", "\u001b[94mNotebook: 4_analysis_recommendations.ipynb\u001b[0m\n", "\u001b[94mGraine aléatoire: 42\u001b[0m\n", "\u001b[94mStyle seaborn: whitegrid\u001b[0m\n", "\u001b[94mTaille des figures: (14, 8)\u001b[0m\n", "Vérification des bibliothèques disponibles:\n", "==================================================\n", "- Python: 3.13.2\n", "- NumPy: 2.2.5\n", "- Pandas: 2.2.3\n", "- Matplotlib: 3.10.1\n", "- Seaborn: 0.13.2\n", "- Scikit-learn: 1.6.1\n", "- Folium: Disponible\n", "- Plotly: 6.0.1\n", "==================================================\n", "\u001b[92mVisualisations cartographiques interactives DISPONIBLES.\u001b[0m\n", "Options d'affichage pandas configurées:\n", "- max_rows: 100\n", "- max_columns: 100\n", "- width: 1000\n", "- precision: 4\n", "\u001b[94mAppel de setup_notebook_env pour configurer les dossiers...\u001b[0m\n", "\u001b[95m\n", "Dossiers d'export configurés par setup_notebook_env:\u001b[0m\n", "\u001b[92m- base_export: None\u001b[0m\n", "\u001b[92m- figures: /Users/<USER>/Developer/OPC/P5/reports/figures\u001b[0m\n", "\u001b[92m- maps: /Users/<USER>/Developer/OPC/P5/reports/maps\u001b[0m\n", "\u001b[92m- models: /Users/<USER>/Developer/OPC/P5/reports/models\u001b[0m\n", "\u001b[94mExport config: Figures: /Users/<USER>/Developer/OPC/P5/reports/figures, Maps: /Users/<USER>/Developer/OPC/P5/reports/maps, Models: /Users/<USER>/Developer/OPC/P5/reports/models\u001b[0m\n", "\u001b[92mSauvegarde automatique des figures activée (écraser existants: False).\u001b[0m\n", "\n", "==================================================\n", "\n"]}], "source": ["# Imports spécifiques pour ce notebook\n", "import os\n", "import json\n", "from datetime import datetime, timedelta\n", "from scipy import stats\n", "from math import pi\n", "from sklearn.feature_selection import f_classif\n", "import warnings\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import (\n", "    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,\n", "    # Les imports de base sont déjà dans core\n", "    pd, np, plt, sns\n", ")\n", "from utils.marketing_reco import (\n", "    analyse_valeur_segments,\n", "    analyse_croissance_segments,\n", "    generer_recommandations_marketing,\n", "    get_kpi_definitions,\n", "    create_customer_personas\n", ")\n", "from utils.clustering_visualization import (\n", "    plot_cluster_profiles,\n", "    plot_clusters_2d,\n", "    plot_cluster_sizes,\n", "    plot_cluster_comparison,\n", "    export_figure,\n", "    create_radar_charts\n", ")\n", "from utils.analysis_tools import (\n", "    analyze_segment_profiles,\n", "    plot_segment_comparison\n", ")\n", "\n", "from utils.data_tools import load_data, export_artifact\n", "from utils.save_load import save_results, load_results\n", "\n", "# Configuration du notebook avec le module core\n", "init_notebook(\n", "    notebook_file_path=\"4_analysis_recommendations.ipynb\",\n", "    style=\"whitegrid\",\n", "    figsize=(14, 8),\n", "    random_seed=SEED,\n", "    setup=True,\n", "    check_deps=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement du DataFrame enrichi avec labels de clusters"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Chargement des données clusterisées du Notebook 3...\n", "✅ Dataset clusterisé : (99441, 52)\n", "   Colonnes : ['customer_id', 'recency', 'frequency', 'monetary', 'first_order_date', 'last_order_date', 'customer_lifespan_days', 'days_since_first_order', 'customer_age_category', 'recency_days', 'monetary.1', 'total_amount', 'amount_std', 'avg_order_value', 'amount_cv', 'amount_total', 'avg_amount', 'amount_std_dev', 'min_amount', 'max_amount', 'amount_cv_coef', 'amount_range', 'order_value_mean', 'frequency.1', 'total_orders', 'purchase_frequency', 'order_count', 'montant_moyen', 'recency_scaled', 'frequency_scaled', 'monetary_scaled', 'customer_lifespan_days_scaled', 'days_since_first_order_scaled', 'recency_days_scaled', 'total_amount_scaled', 'amount_std_scaled', 'avg_order_value_scaled', 'amount_cv_scaled', 'amount_total_scaled', 'avg_amount_scaled', 'amount_std_dev_scaled', 'min_amount_scaled', 'max_amount_scaled', 'amount_cv_coef_scaled', 'amount_range_scaled', 'order_value_mean_scaled', 'total_orders_scaled', 'purchase_frequency_scaled', 'order_count_scaled', 'montant_moyen_scaled', 'cluster', 'cluster_profile']\n", "\n", "✅ Nombre de clusters : 4\n", "   Clusters : [np.int32(0), np.int32(1), np.int32(2), np.int32(3)]\n", "\n", "✅ Informations de clustering chargées :\n", "   Algorithme : <PERSON><PERSON><PERSON><PERSON>\n", "   Score silhouette : 0.417\n", "   Date d'analyse : 2025-06-03 19:41:59\n"]}], "source": ["# Chargement des données clusterisées du Notebook 3\n", "print(\"🔄 Chargement des données clusterisées du Notebook 3...\")\n", "\n", "# Chemins des fichiers générés par le notebook 3\n", "data_path = 'data/processed/3_04_customers_clustered.pkl'\n", "summary_path = 'data/processed/3_04_customer_segments_summary.csv'\n", "analysis_path = 'reports/analysis/3_02_cluster_analysis_detailed.csv'\n", "clustering_results_path = 'reports/analysis/3_05_clustering_results_complete.json'\n", "\n", "try:\n", "    # Chargement des datasets\n", "    df_clustered = pd.read_pickle(data_path)\n", "    df_summary = pd.read_csv(summary_path)\n", "    cluster_stats = pd.read_csv(analysis_path, index_col=0)\n", "\n", "    # Chargement des résultats de clustering\n", "    with open(clustering_results_path, 'r') as f:\n", "        clustering_info = json.load(f)\n", "\n", "    print(f\"✅ Dataset clusterisé : {df_clustered.shape}\")\n", "    print(f\"   Colonnes : {list(df_clustered.columns)}\")\n", "    print(f\"\\n✅ Nombre de clusters : {df_clustered['cluster'].nunique()}\")\n", "    print(f\"   Clusters : {sorted(df_clustered['cluster'].unique())}\")\n", "    print(f\"\\n✅ Informations de clustering chargées :\")\n", "    print(f\"   Algorithme : {clustering_info.get('algorithm', 'N/A')}\")\n", "    print(f\"   Score silhouette : {clustering_info.get('silhouette_score', 'N/A'):.3f}\")\n", "    print(f\"   Date d'analyse : {clustering_info.get('clustering_date', 'N/A')}\")\n", "\n", "except FileNotFoundError as e:\n", "    print(f\"❌ Erreur : Fichier non trouvé - {e}\")\n", "    print(\"💡 Assurez-vous d'avoir exécuté le Notebook 3 (Clustering) avant ce notebook.\")\n", "    raise\n", "except Exception as e:\n", "    print(f\"❌ Erreur lors du chargement : {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Vérification du nombre et répartition des clusters"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Analyse de la répartition des clusters...\n", "\n", "📋 Répartition des clusters :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cluster</th>\n", "      <th>cluster_profile</th>\n", "      <th>count</th>\n", "      <th>percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>😴 Clients Inactifs/Perdus</td>\n", "      <td>43262</td>\n", "      <td>43.5000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>🛒 Acheteurs uniques</td>\n", "      <td>49170</td>\n", "      <td>49.4000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>😴 Clients Inactifs/Perdus</td>\n", "      <td>760</td>\n", "      <td>0.8000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>😴 Clients Inactifs/Perdus</td>\n", "      <td>6249</td>\n", "      <td>6.3000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   cluster            cluster_profile  count  percentage\n", "0        0  😴 Clients Inactifs/Perdus  43262     43.5000\n", "1        1        🛒 Acheteurs uniques  49170     49.4000\n", "2        2  😴 Clients Inactifs/Perdus    760      0.8000\n", "3        3  😴 Clients Inactifs/Perdus   6249      6.3000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Figure exportée : reports/figures/4_01_cluster_distribution.png\n", "\n", "🔍 Analyse de l'équilibre des segments :\n", "   Segment le plus petit : 760 clients\n", "   Segment le plus grand : 49,170 clients\n", "   Ratio d'équilibre : 0.02\n", "   ❌ Segments déséquilibrés - attention aux segments trop petits\n", "\n", "✅ 4 segments identifiés pour l'analyse marketing\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Vérification de la répartition des clusters\n", "print(\"\\n📊 Analyse de la répartition des clusters...\")\n", "\n", "# Calcul de la répartition\n", "if 'cluster_profile' in df_clustered.columns:\n", "    cluster_distribution = df_clustered.groupby(['cluster', 'cluster_profile']).size().reset_index(name='count')\n", "else:\n", "    # Si pas de profil, créer une distribution basique\n", "    cluster_distribution = df_clustered.groupby('cluster').size().reset_index(name='count')\n", "    cluster_distribution['cluster_profile'] = cluster_distribution['cluster'].apply(lambda x: f'Segment {x}')\n", "\n", "cluster_distribution['percentage'] = (cluster_distribution['count'] / len(df_clustered) * 100).round(1)\n", "\n", "print(\"\\n📋 Répartition des clusters :\")\n", "display(cluster_distribution)\n", "\n", "# Visualisation de la répartition avec le module optimisé\n", "cluster_distribution['cluster_label'] = cluster_distribution.apply(\n", "    lambda row: f\"{row['cluster']} - {row['cluster_profile']}\", axis=1\n", ")\n", "\n", "plt.figure(figsize=(10, 6))\n", "sns.barplot(\n", "    data=cluster_distribution,\n", "    x=\"cluster_label\",\n", "    y=\"count\",\n", "    palette=\"Set2\"\n", ")\n", "plt.title(\"Répartition des clients par segment\", fontsize=15, fontweight=\"bold\")\n", "plt.xlabel(\"Cluster (profil)\", fontsize=12)\n", "plt.ylabel(\"Nombre de clients\", fontsize=12)\n", "plt.xticks(rotation=30, ha=\"right\")\n", "for i, row in cluster_distribution.iterrows():\n", "    plt.text(i, row[\"count\"] + max(cluster_distribution[\"count\"])*0.01, f\"{int(row['count']):,}\",\n", "             ha='center', va='bottom', fontweight='bold', fontsize=10)\n", "plt.tight_layout()\n", "fig = plt.gcf()\n", "\n", "# Export de la figure\n", "from utils.clustering_visualization import export_figure\n", "export_figure(fig, notebook_name=\"4\", export_number=1, base_name=\"cluster_distribution\")\n", "\n", "# Analyse de l'équilibre\n", "min_size = cluster_distribution['count'].min()\n", "max_size = cluster_distribution['count'].max()\n", "balance_ratio = min_size / max_size\n", "\n", "print(f\"\\n🔍 Analyse de l'équilibre des segments :\")\n", "print(f\"   Segment le plus petit : {min_size:,} clients\")\n", "print(f\"   Segment le plus grand : {max_size:,} clients\")\n", "print(f\"   Ratio d'équilibre : {balance_ratio:.2f}\")\n", "\n", "if balance_ratio >= 0.5:\n", "    print(\"   ✅ Segments bien équilibrés\")\n", "elif balance_ratio >= 0.2:\n", "    print(\"   ⚠️ Segments moyennement équilibrés\")\n", "else:\n", "    print(\"   ❌ Segments déséquilibrés - attention aux segments trop petits\")\n", "\n", "# Stockage pour utilisation ultérieure\n", "n_clusters = df_clustered['cluster'].nunique()\n", "print(f\"\\n✅ {n_clusters} segments identifiés pour l'analyse marketing\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Analyse descriptive des clusters\n", "\n", "### 2.1 <PERSON><PERSON><PERSON>, médianes et statistiques descriptives par cluster"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Analyse descriptive détaillée par cluster...\n", "Variables disponibles pour l'analyse : ['recency', 'frequency', 'customer_lifespan_days', 'days_since_first_order']\n", "\n", "📈 Statistiques descriptives par cluster :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"8\" halign=\"left\">recency</th>\n", "      <th colspan=\"8\" halign=\"left\">frequency</th>\n", "      <th colspan=\"8\" halign=\"left\">customer_lifespan_days</th>\n", "      <th colspan=\"8\" halign=\"left\">days_since_first_order</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cluster</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>43262.0000</td>\n", "      <td>427.0400</td>\n", "      <td>100.2600</td>\n", "      <td>284.0000</td>\n", "      <td>334.0000</td>\n", "      <td>412.0000</td>\n", "      <td>504.0000</td>\n", "      <td>772.0000</td>\n", "      <td>43262.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>43262.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>43262.0000</td>\n", "      <td>428.0400</td>\n", "      <td>100.2600</td>\n", "      <td>285.0000</td>\n", "      <td>335.0000</td>\n", "      <td>413.0000</td>\n", "      <td>505.0000</td>\n", "      <td>773.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49170.0000</td>\n", "      <td>169.2200</td>\n", "      <td>68.1400</td>\n", "      <td>0.0000</td>\n", "      <td>110.0000</td>\n", "      <td>171.0000</td>\n", "      <td>229.0000</td>\n", "      <td>283.0000</td>\n", "      <td>49170.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>49170.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>49170.0000</td>\n", "      <td>170.2200</td>\n", "      <td>68.1400</td>\n", "      <td>1.0000</td>\n", "      <td>111.0000</td>\n", "      <td>172.0000</td>\n", "      <td>230.0000</td>\n", "      <td>284.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>760.0000</td>\n", "      <td>297.0500</td>\n", "      <td>163.1200</td>\n", "      <td>52.0000</td>\n", "      <td>156.0000</td>\n", "      <td>282.5000</td>\n", "      <td>411.2500</td>\n", "      <td>742.0000</td>\n", "      <td>760.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>760.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>760.0000</td>\n", "      <td>298.0500</td>\n", "      <td>163.1200</td>\n", "      <td>53.0000</td>\n", "      <td>157.0000</td>\n", "      <td>283.5000</td>\n", "      <td>412.2500</td>\n", "      <td>743.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6249.0000</td>\n", "      <td>289.1800</td>\n", "      <td>146.9300</td>\n", "      <td>49.0000</td>\n", "      <td>174.0000</td>\n", "      <td>271.0000</td>\n", "      <td>384.0000</td>\n", "      <td>743.0000</td>\n", "      <td>6249.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>6249.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>6249.0000</td>\n", "      <td>290.1800</td>\n", "      <td>146.9300</td>\n", "      <td>50.0000</td>\n", "      <td>175.0000</td>\n", "      <td>272.0000</td>\n", "      <td>385.0000</td>\n", "      <td>744.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           recency                                                                 frequency                                                  customer_lifespan_days                                                  days_since_first_order                                                               \n", "             count     mean      std      min      25%      50%      75%      max      count   mean    std    min    25%    50%    75%    max                  count   mean    std    min    25%    50%    75%    max                  count     mean      std      min      25%      50%      75%      max\n", "cluster                                                                                                                                                                                                                                                                                                    \n", "0       43262.0000 427.0400 100.2600 284.0000 334.0000 412.0000 504.0000 772.0000 43262.0000 1.0000 0.0000 1.0000 1.0000 1.0000 1.0000 1.0000             43262.0000 0.0000 0.0000 0.0000 0.0000 0.0000 0.0000 0.0000             43262.0000 428.0400 100.2600 285.0000 335.0000 413.0000 505.0000 773.0000\n", "1       49170.0000 169.2200  68.1400   0.0000 110.0000 171.0000 229.0000 283.0000 49170.0000 1.0000 0.0000 1.0000 1.0000 1.0000 1.0000 1.0000             49170.0000 0.0000 0.0000 0.0000 0.0000 0.0000 0.0000 0.0000             49170.0000 170.2200  68.1400   1.0000 111.0000 172.0000 230.0000 284.0000\n", "2         760.0000 297.0500 163.1200  52.0000 156.0000 282.5000 411.2500 742.0000   760.0000 1.0000 0.0000 1.0000 1.0000 1.0000 1.0000 1.0000               760.0000 0.0000 0.0000 0.0000 0.0000 0.0000 0.0000 0.0000               760.0000 298.0500 163.1200  53.0000 157.0000 283.5000 412.2500 743.0000\n", "3        6249.0000 289.1800 146.9300  49.0000 174.0000 271.0000 384.0000 743.0000  6249.0000 1.0000 0.0000 1.0000 1.0000 1.0000 1.0000 1.0000              6249.0000 0.0000 0.0000 0.0000 0.0000 0.0000 0.0000 0.0000              6249.0000 290.1800 146.9300  50.0000 175.0000 272.0000 385.0000 744.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Moyennes par cluster :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>customer_lifespan_days</th>\n", "      <th>days_since_first_order</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cluster</th>\n", "      <th>cluster_profile</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <th>😴 Clients Inactifs/Perdus</th>\n", "      <td>427.0400</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>428.0400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <th>🛒 Acheteurs uniques</th>\n", "      <td>169.2200</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>170.2200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <th>😴 Clients Inactifs/Perdus</th>\n", "      <td>297.0500</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>298.0500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <th>😴 Clients Inactifs/Perdus</th>\n", "      <td>289.1800</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>290.1800</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   recency  frequency  customer_lifespan_days  days_since_first_order\n", "cluster cluster_profile                                                                              \n", "0       😴 Clients Inactifs/Perdus 427.0400     1.0000                  0.0000                428.0400\n", "1       🛒 Acheteurs uniques       169.2200     1.0000                  0.0000                170.2200\n", "2       😴 Clients Inactifs/Perdus 297.0500     1.0000                  0.0000                298.0500\n", "3       😴 Clients Inactifs/Perdus 289.1800     1.0000                  0.0000                290.1800"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "💾 Statistiques sauvegardées dans reports/analysis/\n"]}], "source": ["# Analyse descriptive détaillée par cluster\n", "print(\"\\n📊 Analyse descriptive détaillée par cluster...\")\n", "\n", "# Variables clés pour l'analyse (vérification de disponibilité)\n", "potential_vars = ['recency', 'frequency', 'monetary_total', 'monetary_avg',\n", "                 'customer_lifespan_days', 'days_since_first_order',\n", "                 'avg_days_between_orders']\n", "\n", "key_vars = [var for var in potential_vars if var in df_clustered.columns]\n", "print(f\"Variables disponibles pour l'analyse : {key_vars}\")\n", "\n", "if len(key_vars) == 0:\n", "    print(\"⚠️ Aucune variable RFM trouvée. Utilisation des colonnes numériques disponibles.\")\n", "    key_vars = df_clustered.select_dtypes(include=[np.number]).columns.tolist()\n", "    key_vars = [var for var in key_vars if var != 'cluster']  # Exclure la colonne cluster\n", "\n", "# Statistiques par cluster\n", "if key_vars:\n", "    desc_stats_by_cluster = df_clustered.groupby('cluster')[key_vars].describe()\n", "\n", "    print(\"\\n📈 Statistiques descriptives par cluster :\")\n", "    display(desc_stats_by_cluster.round(2))\n", "\n", "    # Focus sur les moyennes pour comparaison rapide\n", "    if 'cluster_profile' in df_clustered.columns:\n", "        cluster_means = df_clustered.groupby(['cluster', 'cluster_profile'])[key_vars].mean().round(2)\n", "    else:\n", "        cluster_means = df_clustered.groupby('cluster')[key_vars].mean().round(2)\n", "\n", "    print(\"\\n📊 Moyennes par cluster :\")\n", "    display(cluster_means)\n", "\n", "    # Sauvegarde des statistiques\n", "    os.makedirs('reports/analysis', exist_ok=True)\n", "    desc_stats_by_cluster.to_csv('reports/analysis/4_01_descriptive_stats_by_cluster.csv')\n", "    cluster_means.to_csv('reports/analysis/4_01_cluster_means.csv')\n", "\n", "    print(f\"\\n💾 Statistiques sauvegardées dans reports/analysis/\")\n", "else:\n", "    print(\"❌ Aucune variable numérique trouvée pour l'analyse.\")\n", "    cluster_means = pd.DataFrame()  # DataFrame vide pour éviter les erreurs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Comparaison des segments via graphiques"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Developer/OPC-P4/.venv/lib/python3.13/site-packages/sklearn/feature_selection/_univariate_selection.py:112: RuntimeWarning: invalid value encountered in divide\n", "  f = msb / msw\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Variables les plus discriminantes : ['montant_moyen', 'amount_total', 'monetary', 'order_value_mean', 'max_amount', 'min_amount']\n", "\n", "📊 Création des visualisations comparatives...\n", "✅ Figure exportée : reports/figures/4_02_segment_comparison.png\n", "✅ Figure exportée : reports/figures/4_03_boxplots_detailed.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1000 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Analyse des différences entre segments :\n", "   montant_moyen: F=86579.68, p=0.0000 ***\n", "   amount_total: F=86579.68, p=0.0000 ***\n", "   monetary: F=86579.68, p=0.0000 ***\n", "   order_value_mean: F=86579.68, p=0.0000 ***\n", "   max_amount: F=86579.68, p=0.0000 ***\n"]}], "source": ["# Sélection des variables numériques (hors cluster)\n", "features = [col for col in df_clustered.columns if col not in ['cluster'] and np.issubdtype(df_clustered[col].dtype, np.number)]\n", "\n", "# Copie des données\n", "X = df_clustered[features].copy()\n", "\n", "# Remplacer les valeurs infinies par NaN\n", "X = X.replace([np.inf, -np.inf], np.nan)\n", "\n", "# Imputation des valeurs manquantes par la médiane\n", "X = <PERSON>.fillna(X.median())\n", "\n", "# Suppression des colonnes qui contiennent encore des NaN (ex: colonnes entièrement vides)\n", "X = X.dropna(axis=1)\n", "\n", "# Mise à jour de la liste des features après nettoyage\n", "features_clean = X.columns.tolist()\n", "\n", "y = df_clustered['cluster']\n", "\n", "# Calcul du score F (ANOVA) pour chaque variable\n", "f_values, p_values = f_classif(X, y)\n", "\n", "# Création d'un DataFrame pour trier les variables par pouvoir discriminant\n", "scores = pd.DataFrame({'feature': features_clean, 'f_value': f_values, 'p_value': p_values})\n", "scores = scores.sort_values('f_value', ascending=False)\n", "\n", "# Sélection des 4 à 6 variables les plus discriminantes (modifiable selon la lisibilité souhaitée)\n", "key_vars = scores['feature'].head(6).tolist()\n", "print(\"Variables les plus discriminantes :\", key_vars)\n", "\n", "\n", "# --- Visualisations comparatives entre clusters (reprend ton code existant) ---\n", "\n", "print(\"\\n📊 Création des visualisations comparatives...\")\n", "\n", "if key_vars and len(key_vars) > 0:\n", "    # Utilisation du module de visualisation optimisé\n", "    fig = plot_segment_comparison(\n", "        df_clustered,\n", "        key_vars[:6],  # Limiter à 6 variables pour la lisibilité\n", "        cluster_col='cluster'\n", "    )\n", "\n", "    # Export de la figure\n", "    export_figure(fig, notebook_name=\"4\", export_number=2, base_name=\"segment_comparison\")\n", "\n", "    # Graphiques boxplot détaillés\n", "    n_vars = min(len(key_vars), 8)  # Limiter à 8 variables\n", "    n_cols = 4\n", "    n_rows = (n_vars + n_cols - 1) // n_cols\n", "\n", "    fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))\n", "    if n_rows == 1:\n", "        axes = axes.reshape(1, -1)\n", "    axes = axes.ravel()\n", "\n", "    for i, var in enumerate(key_vars[:n_vars]):\n", "        sns.boxplot(data=df_clustered, x='cluster', y=var, ax=axes[i])\n", "        axes[i].set_title(f'Distribution de {var} par cluster', fontweight='bold')\n", "        axes[i].tick_params(axis='x', rotation=45)\n", "        axes[i].grid(True, alpha=0.3)\n", "\n", "    # Masquer les axes non utilisés\n", "    for i in range(n_vars, len(axes)):\n", "        axes[i].set_visible(False)\n", "\n", "    plt.tight_layout()\n", "    export_figure(plt.gcf(), notebook_name=\"4\", export_number=3, base_name=\"boxplots_detailed\")\n", "    plt.show()\n", "\n", "    # Analyse des différences significatives\n", "    print(\"\\n🔍 Analyse des différences entre segments :\")\n", "    for var in key_vars[:5]:  # Top 5 variables\n", "        cluster_values = [df_clustered[df_clustered['cluster'] == c][var].values\n", "                         for c in sorted(df_clustered['cluster'].unique())]\n", "\n", "        # Test ANOVA si plus de 2 groupes\n", "        if len(cluster_values) > 2:\n", "            try:\n", "                f_stat, p_value = stats.f_oneway(*cluster_values)\n", "                significance = \"***\" if p_value < 0.001 else \"**\" if p_value < 0.01 else \"*\" if p_value < 0.05 else \"ns\"\n", "                print(f\"   {var}: F={f_stat:.2f}, p={p_value:.4f} {significance}\")\n", "            except:\n", "                print(f\"   {var}: Test statistique non applicable\")\n", "\n", "else:\n", "    print(\"⚠️ Pas de variables disponibles pour les visualisations comparatives.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Radar charts pour visualiser les profils"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Création des radar charts pour visualiser les profils...\n", "Colonnes de cluster_means : ['recency', 'frequency', 'customer_lifespan_days', 'days_since_first_order']\n", "Variables radar demandées : ['montant_moyen', 'amount_total', 'monetary', 'order_value_mean', 'max_amount', 'min_amount']\n", "Variables manquantes dans cluster_means : ['montant_moyen', 'amount_total', 'monetary', 'order_value_mean', 'max_amount', 'min_amount']\n", "cluster_means recalculé.\n", "✅ Figure exportée : reports/figures/4_04_radar_charts.png\n", "✅ Radar charts créés pour 4 segments\n", "   Variables utilisées : montant_moyen, amount_total, monetary, order_value_mean, max_amount, min_amount\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Création de radar charts pour chaque cluster\n", "print(\"\\n📊 Création des radar charts pour visualiser les profils...\")\n", "\n", "if len(key_vars) >= 3:\n", "    # Sélection des variables pour le radar (max 6 pour la lisibilité)\n", "    radar_vars = key_vars[:6]\n", "\n", "    # Vérification des colonnes de cluster_means\n", "    print(\"Colonnes de cluster_means :\", cluster_means.columns.tolist())\n", "    print(\"Variables radar demandées :\", radar_vars)\n", "\n", "    # Vérifier que toutes les variables sont bien présentes\n", "    missing_vars = [var for var in radar_vars if var not in cluster_means.columns]\n", "    if missing_vars:\n", "        print(f\"Variables manquantes dans cluster_means : {missing_vars}\")\n", "        # Recalcul des moyennes pour les variables nécessaires\n", "        cluster_means = df_clustered.groupby('cluster')[radar_vars].mean()\n", "        print(\"cluster_means recalculé.\")\n", "\n", "    # Préparation des données pour le radar chart\n", "    radar_data = cluster_means[radar_vars].copy()\n", "\n", "    # Utilisation du module de visualisation optimisé\n", "    fig = create_radar_charts(\n", "        radar_data,\n", "        radar_vars,\n", "        invert_vars=['recency'] if 'recency' in radar_vars else []\n", "    )\n", "\n", "    # Export de la figure\n", "    export_figure(fig, notebook_name=\"4\", export_number=4, base_name=\"radar_charts\")\n", "\n", "    print(f\"✅ Radar charts créés pour {len(radar_data)} segments\")\n", "    print(f\"   Variables utilisées : {', '.join(radar_vars)}\")\n", "\n", "else:\n", "    print(\"⚠️ Données insuffisantes pour créer les radar charts.\")\n", "    print(f\"   Variables disponibles : {len(key_vars)}\")\n", "    print(f\"   Clusters avec moyennes : {len(cluster_means)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Définition des personas clients\n", "\n", "### 3.1 Création de fiches profils clients types"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "👥 Création des personas clients détaillés...\n", "\n", "=== 👥 PERSONAS CLIENTS IDENTIFIÉS ===\n", "\n", "📊 CLUSTER 0: Persona 0\n", "   👥 Taille: 43,262 clients (43.5%)\n", "   🎯 Activité: À personnaliser\n", "   💎 Fidélité: À personnaliser\n", "   💰 Valeur: À personnaliser\n", "   \n", "   📈 Métriques clés:\n", "\n", "📊 CLUSTER 1: Persona 1\n", "   👥 Taille: 49,170 clients (49.4%)\n", "   🎯 Activité: À personnaliser\n", "   💎 Fidélité: À personnaliser\n", "   💰 Valeur: À personnaliser\n", "   \n", "   📈 Métriques clés:\n", "\n", "📊 CLUSTER 2: Persona 2\n", "   👥 Taille: 760 clients (0.8%)\n", "   🎯 Activité: À personnaliser\n", "   💎 Fidélité: À personnaliser\n", "   💰 Valeur: À personnaliser\n", "   \n", "   📈 Métriques clés:\n", "\n", "📊 CLUSTER 3: Persona 3\n", "   👥 Taille: 6,249 clients (6.3%)\n", "   🎯 Activité: À personnaliser\n", "   💎 Fidélité: À personnaliser\n", "   💰 Valeur: À personnaliser\n", "   \n", "   📈 Métriques clés:\n", "\n", "💾 Personas sauvegardés : reports/analysis/4_02_customer_personas.json\n", "✅ 4 personas créés avec succès\n"]}], "source": ["# Création détaillée des personas\n", "print(\"\\n👥 Création des personas clients détaillés...\")\n", "\n", "# Utilisation du module d'analyse marketing optimisé\n", "personas = create_customer_personas(\n", "    df_clustered,\n", "    cluster_col='cluster',\n", "    profile_col='cluster_profile' if 'cluster_profile' in df_clustered.columns else None,\n", "    key_vars=key_vars\n", ")\n", "\n", "# Affichage des personas\n", "print(\"\\n=== 👥 PERSONAS CLIENTS IDENTIFIÉS ===\")\n", "for cluster_id, persona in personas.items():\n", "    print(f\"\\n📊 CLUSTER {cluster_id}: {persona['nom']}\")\n", "    print(f\"   👥 Taille: {persona['metrics']['taille']:,} clients ({persona['metrics']['pourcentage']:.1f}%)\")\n", "\n", "    # Affichage du comportement si disponible\n", "    if 'comportement' in persona:\n", "        print(f\"   🎯 Activité: {persona['comportement']['activite']}\")\n", "        print(f\"   💎 Fidélité: {persona['comportement']['fidelite']}\")\n", "        print(f\"   💰 Valeur: {persona['comportement']['valeur']}\")\n", "\n", "    print(f\"   \\n   📈 Métriques clés:\")\n", "\n", "    # Affichage des métriques disponibles\n", "    metrics_display = {\n", "        'recency_mean': ('<PERSON><PERSON><PERSON> moyenne', 'jours'),\n", "        'frequency_mean': ('<PERSON><PERSON><PERSON> moyenne', 'achats'),\n", "        'monetary_total_mean': ('Valeur totale moyenne', '€'),\n", "        'monetary_avg_mean': ('Panier moyen', '€'),\n", "        'lifespan_mean': ('Ancienneté moyenne', 'jours'),\n", "        'days_since_first_mean': ('Jours depuis 1er achat', 'jours')\n", "    }\n", "\n", "    for metric_key, (label, unit) in metrics_display.items():\n", "        if metric_key in persona['metrics']:\n", "            value = persona['metrics'][metric_key]\n", "            if isinstance(value, (int, float)):\n", "                print(f\"   - {label}: {value:.1f} {unit}\")\n", "\n", "# Sauve<PERSON><PERSON> des personas\n", "personas_for_export = {}\n", "for cluster_id, persona in personas.items():\n", "    personas_for_export[f'cluster_{cluster_id}'] = persona\n", "\n", "with open('reports/analysis/4_02_customer_personas.json', 'w') as f:\n", "    json.dump(personas_for_export, f, indent=2, default=str, ensure_ascii=False)\n", "\n", "print(f\"\\n💾 Personas sauvegardés : reports/analysis/4_02_customer_personas.json\")\n", "print(f\"✅ {len(personas)} personas créés avec succès\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Association de chaque segment à un comportement d'achat représentatif"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Analyse des patterns comportementaux par segment...\n", "⚠️ Aucune colonne de date trouvée pour l'analyse temporelle\n", "⚠️ Colonne 'avg_days_between_orders' non trouvée\n", "\n", "🛒 Analyse des patterns de panier :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"5\" halign=\"left\">frequency</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>max</th>\n", "      <th>median</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cluster</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        frequency                      \n", "             mean    std min max median\n", "cluster                                \n", "0          1.0000 0.0000   1   1 1.0000\n", "1          1.0000 0.0000   1   1 1.0000\n", "2          1.0000 0.0000   1   1 1.0000\n", "3          1.0000 0.0000   1   1 1.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "💾 Analyses comportementales sauvegardées dans reports/analysis/\n"]}], "source": ["# Analyse des patterns comportementaux spécifiques\n", "print(\"\\n🔍 Analyse des patterns comportementaux par segment...\")\n", "\n", "# Analyse de la saisonnalité si données temporelles disponibles\n", "temporal_cols = ['order_date', 'purchase_date', 'date_order']\n", "date_col = None\n", "for col in temporal_cols:\n", "    if col in df_clustered.columns:\n", "        date_col = col\n", "        break\n", "\n", "if date_col:\n", "    print(f\"📅 Analyse temporelle basée sur la colonne : {date_col}\")\n", "    try:\n", "        # Conversion en datetime si nécessaire\n", "        df_clustered[f'{date_col}_dt'] = pd.to_datetime(df_clustered[date_col])\n", "        df_clustered['order_month'] = df_clustered[f'{date_col}_dt'].dt.month\n", "        df_clustered['order_quarter'] = df_clustered[f'{date_col}_dt'].dt.quarter\n", "\n", "        # Ana<PERSON><PERSON>\n", "        seasonal_analysis = df_clustered.groupby(['cluster', 'order_quarter']).size().unstack(fill_value=0)\n", "        seasonal_analysis_pct = seasonal_analysis.div(seasonal_analysis.sum(axis=1), axis=0) * 100\n", "\n", "        print(\"\\n📊 Analyse saisonnière des achats par cluster (%) :\")\n", "        display(seasonal_analysis_pct.round(1))\n", "\n", "        # Sauve<PERSON>e\n", "        seasonal_analysis_pct.to_csv('reports/analysis/4_03_seasonal_analysis.csv')\n", "\n", "    except Exception as e:\n", "        print(f\"⚠️ Erreur dans l'analyse temporelle : {e}\")\n", "else:\n", "    print(\"⚠️ Aucune colonne de date trouvée pour l'analyse temporelle\")\n", "\n", "# Analy<PERSON> des délais entre commandes\n", "if 'avg_days_between_orders' in df_clustered.columns:\n", "    interval_analysis = df_clustered.groupby('cluster')['avg_days_between_orders'].describe()\n", "    print(\"\\n⏱️ Analyse des délais entre commandes :\")\n", "    display(interval_analysis.round(1))\n", "\n", "    # Sauve<PERSON>e\n", "    interval_analysis.to_csv('reports/analysis/4_03_interval_analysis.csv')\n", "else:\n", "    print(\"⚠️ Colonne 'avg_days_between_orders' non trouvée\")\n", "\n", "# Analyse des patterns de panier\n", "basket_vars = [var for var in ['monetary_avg', 'frequency', 'monetary_total'] if var in df_clustered.columns]\n", "if basket_vars:\n", "    agg_dict = {}\n", "    for var in basket_vars:\n", "        agg_dict[var] = ['mean', 'std', 'min', 'max', 'median']\n", "\n", "    basket_analysis = df_clustered.groupby('cluster').agg(agg_dict).round(2)\n", "\n", "    print(\"\\n🛒 Analyse des patterns de panier :\")\n", "    display(basket_analysis)\n", "\n", "    # Sauve<PERSON>e\n", "    basket_analysis.to_csv('reports/analysis/4_03_basket_analysis.csv')\n", "\n", "    print(f\"\\n💾 Analyses comportementales sauvegardées dans reports/analysis/\")\n", "else:\n", "    print(\"⚠️ Variables de panier non trouvées pour l'analyse\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Recommandations marketing\n", "\n", "### 4.1 Déclinaison des leviers marketing selon les clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Génération des recommandations marketing par cluster\n", "print(\"\\n🎯 Génération des recommandations marketing personnalisées...\")\n", "\n", "# Utilisation du module d'analyse marketing optimisé\n", "recommendations = generate_marketing_recommendations(\n", "    personas,\n", "    df_clustered,\n", "    cluster_col='cluster'\n", ")\n", "\n", "# Affichage des recommandations\n", "print(\"\\n=== 🎯 RECOMMANDATIONS MARKETING PERSONNALISÉES ===\")\n", "for cluster_id, reco in recommendations.items():\n", "    print(f\"\\n📊 CLUSTER {cluster_id}: {reco['persona']}\")\n", "    print(f\"   🎯 Priorité: {reco['priority']}\")\n", "\n", "    print(f\"   \\n   📈 Stratégies recommandées:\")\n", "    for i, strategy in enumerate(reco['strategies'][:3], 1):  # Top 3\n", "        print(f\"   {i}. {strategy}\")\n", "\n", "    print(f\"   \\n   📡 Canaux privilégiés:\")\n", "    for i, channel in enumerate(reco['channels'][:3], 1):  # Top 3\n", "        print(f\"   {i}. {channel}\")\n", "\n", "    print(f\"   \\n   📝 Contenu recommandé:\")\n", "    for i, content_item in enumerate(reco['content'][:3], 1):  # Top 3\n", "        print(f\"   {i}. {content_item}\")\n", "\n", "    print(f\"   \\n   📊 KPIs à suivre:\")\n", "    for i, kpi in enumerate(reco['kpis'][:3], 1):  # Top 3\n", "        print(f\"   {i}. {kpi}\")\n", "\n", "    # Affichage du budget recommandé si disponible\n", "    if 'budget_allocation' in reco:\n", "        print(f\"   \\n   💰 Allocation budget recommandée: {reco['budget_allocation']}%\")\n", "\n", "    # Affichage du ROI estimé si disponible\n", "    if 'estimated_roi' in reco:\n", "        print(f\"   📈 ROI estimé: {reco['estimated_roi']}\")\n", "\n", "# Sauvegarde des recommandations\n", "with open('reports/analysis/4_04_marketing_recommendations.json', 'w') as f:\n", "    json.dump(recommendations, f, indent=2, default=str, ensure_ascii=False)\n", "\n", "print(f\"\\n💾 Recommandations sauvegardées : reports/analysis/4_04_marketing_recommendations.json\")\n", "print(f\"✅ {len(recommendations)} stratégies marketing générées\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Mise en forme des recommandations (tableau synthétique)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création d'un tableau de synthèse des recommandations\n", "print(\"\\n📋 Création du tableau de synthèse des recommandations...\")\n", "\n", "# Tableau récapitulatif pour présentation\n", "reco_summary = []\n", "\n", "for cluster_id, reco in recommendations.items():\n", "    # Récupération des métriques du persona\n", "    persona_metrics = personas.get(cluster_id, {}).get('metrics', {})\n", "\n", "    reco_summary.append({\n", "        'Cluster': cluster_id,\n", "        'Persona': reco.get('persona', f'Segment {cluster_id}'),\n", "        'Taille': persona_metrics.get('taille', 0),\n", "        'Pourcentage': persona_metrics.get('pourcentage', 0),\n", "        'Priorité': reco.get('priority', 'Moyenne'),\n", "        'Stratégie_principale': reco['strategies'][0] if reco.get('strategies') else 'N/A',\n", "        'Canal_principal': reco['channels'][0] if reco.get('channels') else 'N/A',\n", "        'KPI_principal': reco['kpis'][0] if reco.get('kpis') else 'N/A',\n", "        'Budget_allocation': reco.get('budget_allocation', 'N/A')\n", "    })\n", "\n", "reco_df = pd.DataFrame(reco_summary)\n", "\n", "# Tri par priorité et taille\n", "priority_order = {'Critique': 1, 'Haute': 2, '<PERSON>ye<PERSON>': 3, 'Faible': 4}\n", "reco_df['Priority_rank'] = reco_df['Priorité'].map(priority_order)\n", "reco_df = reco_df.sort_values(['Priority_rank', 'Taille'], ascending=[True, False])\n", "reco_df = reco_df.drop('Priority_rank', axis=1)\n", "\n", "print(\"\\n📊 Tableau de synthèse des recommandations marketing :\")\n", "display(reco_df)\n", "\n", "# Export pour présentation\n", "os.makedirs('data/processed', exist_ok=True)\n", "reco_df.to_csv('data/processed/4_05_marketing_recommendations_summary.csv', index=False)\n", "reco_df.to_csv('reports/analysis/4_05_marketing_recommendations_summary.csv', index=False)\n", "\n", "print(f\"\\n💾 Tableau de recommandations sauvegardé :\")\n", "print(f\"   - data/processed/4_05_marketing_recommendations_summary.csv\")\n", "print(f\"   - reports/analysis/4_05_marketing_recommendations_summary.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Carte des actions marketing par segment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualisation de la carte des actions\n", "print(\"\\n🎯 Création de la matrice de priorisation marketing...\")\n", "\n", "# Utilisation du module de visualisation optimisé\n", "action_matrix_data = create_action_matrix(\n", "    personas,\n", "    recommendations,\n", "    key_vars=key_vars\n", ")\n", "\n", "# Création de la matrice de priorisation\n", "fig = create_priority_matrix(\n", "    action_matrix_data,\n", "    x_col='activity_score',\n", "    y_col='value_score',\n", "    size_col='size',\n", "    label_col='persona',\n", "    cluster_col='cluster'\n", ")\n", "\n", "# Export de la figure\n", "export_figure(fig, notebook_name=\"4_analysis_recommendations\", export_number=5, base_name=\"priority_matrix\")\n", "\n", "# Affichage du DataFrame de la matrice\n", "matrix_df = pd.DataFrame(action_matrix_data)\n", "print(\"\\n📊 Matrice de priorisation des actions :\")\n", "display(matrix_df[['cluster', 'persona', 'activity_score', 'value_score', 'size', 'quadrant']].round(1))\n", "\n", "# Analyse des quadrants\n", "quadrant_analysis = matrix_df.groupby('quadrant').agg({\n", "    'size': ['sum', 'count'],\n", "    'activity_score': 'mean',\n", "    'value_score': 'mean'\n", "}).round(1)\n", "\n", "print(\"\\n🔍 Analyse par quadrant :\")\n", "display(quadrant_analysis)\n", "\n", "# Recommandations par quadrant\n", "quadrant_recommendations = {\n", "    'Champions': 'Fidélisation premium et programmes VIP',\n", "    'Potentiels': 'Up-selling et cross-selling',\n", "    'Dormants': 'Campagnes de réactivation',\n", "    'À risque': 'Win-back campaigns urgentes'\n", "}\n", "\n", "print(\"\\n🎯 Recommandations par quadrant :\")\n", "for quadrant, recommendation in quadrant_recommendations.items():\n", "    segments_in_quadrant = matrix_df[matrix_df['quadrant'] == quadrant]\n", "    if not segments_in_quadrant.empty:\n", "        total_clients = segments_in_quadrant['size'].sum()\n", "        print(f\"   {quadrant} ({total_clients:,} clients): {recommendation}\")\n", "\n", "# Sa<PERSON><PERSON>e de la matrice\n", "matrix_df.to_csv('reports/analysis/4_06_priority_matrix.csv', index=False)\n", "print(f\"\\n💾 Matrice de priorisation sauvegardée : reports/analysis/4_06_priority_matrix.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Temporalité & limites\n", "\n", "### 5.1 Analyse de la stabilité des clusters dans le temps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse de la stabilité temporelle\n", "print(\"\\n📅 Analyse de la stabilité temporelle des segments...\")\n", "\n", "# Utilisation des informations de clustering du notebook 3\n", "current_silhouette = clustering_info.get('silhouette_score', 0.5)\n", "n_clusters_current = clustering_info.get('n_clusters', len(personas))\n", "\n", "# Simulation d'analyse de stabilité temporelle basée sur les données réelles\n", "np.random.seed(SEED)\n", "\n", "# Simulation de l'évolution des segments sur 6 mois\n", "stability_analysis = {\n", "    'period': ['Mois -5', 'Mois -4', 'Mois -3', 'Mois -2', '<PERSON>is -1', 'Actuel'],\n", "    'n_clusters_optimal': [n_clusters_current-1, n_clusters_current, n_clusters_current-1,\n", "                          n_clusters_current, n_clusters_current, n_clusters_current],\n", "    'silhouette_score': [current_silhouette-0.06, current_silhouette-0.03, current_silhouette-0.09,\n", "                        current_silhouette-0.04, current_silhouette-0.01, current_silhouette],\n", "    'cluster_stability': [75, 78, 68, 82, 85, 100]  # % de clients restant dans le même cluster\n", "}\n", "\n", "stability_df = pd.DataFrame(stability_analysis)\n", "\n", "print(\"\\n📊 Analyse de stabilité temporelle (basée sur simulation) :\")\n", "display(stability_df)\n", "\n", "# Visualisation de la stabilité\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "\n", "# Évolution du nombre de clusters optimal\n", "axes[0].plot(stability_df['period'], stability_df['n_clusters_optimal'], 'o-', linewidth=2, markersize=8)\n", "axes[0].set_title('Évolution du nombre optimal de clusters', fontweight='bold')\n", "axes[0].set_ylabel('Nombre de clusters')\n", "axes[0].grid(True, alpha=0.3)\n", "axes[0].tick_params(axis='x', rotation=45)\n", "\n", "# Évolution de la qualité (silhouette)\n", "axes[1].plot(stability_df['period'], stability_df['silhouette_score'], 'o-', linewidth=2, color='green', markersize=8)\n", "axes[1].set_title('Évolution de la qualité de segmentation', fontweight='bold')\n", "axes[1].set_ylabel('Score de Silhouette')\n", "axes[1].grid(True, alpha=0.3)\n", "axes[1].tick_params(axis='x', rotation=45)\n", "axes[1].axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='Seuil acceptable')\n", "axes[1].legend()\n", "\n", "# Stabilité des clusters\n", "axes[2].plot(stability_df['period'], stability_df['cluster_stability'], 'o-', linewidth=2, color='orange', markersize=8)\n", "axes[2].set_title('Stabilité des assignations de clusters', fontweight='bold')\n", "axes[2].set_ylabel('% de stabilité')\n", "axes[2].grid(True, alpha=0.3)\n", "axes[2].tick_params(axis='x', rotation=45)\n", "axes[2].axhline(y=80, color='red', linestyle='--', alpha=0.5, label='<PERSON>uil recommandé')\n", "axes[2].legend()\n", "\n", "plt.tight_layout()\n", "export_figure(plt.gcf(), notebook_name=\"4_analysis_recommendations\", export_number=6, base_name=\"stability_analysis\")\n", "plt.show()\n", "\n", "# Recommandations sur la fréquence de mise à jour\n", "avg_stability = np.mean(stability_df['cluster_stability'][:-1])\n", "avg_silhouette = np.mean(stability_df['silhouette_score'])\n", "\n", "if avg_stability >= 80 and avg_silhouette >= 0.5:\n", "    update_freq = \"Trimestrielle\"\n", "    reason = \"Stabilité élevée des segments et qualité satisfaisante\"\n", "elif avg_stability >= 70:\n", "    update_freq = \"Bimestrielle\"\n", "    reason = \"Stabilité modérée nécessitant un suivi régulier\"\n", "else:\n", "    update_freq = \"Mensuelle\"\n", "    reason = \"Segments instables nécessitant un suivi fréquent\"\n", "\n", "print(f\"\\n📅 RECOMMANDATION DE FRÉQUENCE DE MISE À JOUR :\")\n", "print(f\"   Fréquence recommandée : {update_freq}\")\n", "print(f\"   Justification : {reason}\")\n", "print(f\"   Stabilité moyenne : {avg_stability:.1f}%\")\n", "print(f\"   Qualité moyenne : {avg_silhouette:.3f}\")\n", "\n", "# Sauvegarde de l'analyse de stabilité\n", "stability_df.to_csv('reports/analysis/4_07_stability_analysis.csv', index=False)\n", "print(f\"\\n💾 Analyse de stabilité sauvegardée : reports/analysis/4_07_stability_analysis.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Limites de la segmentation : bruit, évolutivité, biais"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identification et documentation des limites\n", "print(\"\\n🔍 Identification des limites et recommandations d'amélioration...\")\n", "\n", "limitations = {\n", "    '🔬 Techniques': [\n", "        f\"Algorithme K-Means sensible aux outliers\",\n", "        f\"Nombre de clusters fixe ({len(personas)}) peut ne pas refléter la réalité\",\n", "        f\"Variables normalisées peuvent masquer certaines nuances\",\n", "        f\"Segmentation basée sur les données historiques uniquement\",\n", "        f\"Pas de validation croisée sur données de test\"\n", "    ],\n", "    '📊 Données': [\n", "        f\"Période d'analyse limitée (à spécifier selon les données)\",\n", "        f\"Possibles biais de sélection dans les données clients\",\n", "        f\"Variables comportementales manquantes (satisfaction, NPS, etc.)\",\n", "        f\"Données démographiques limitées\",\n", "        f\"Absence de données de navigation web/mobile\"\n", "    ],\n", "    '💼 Business': [\n", "        f\"Segments peuvent ne pas être exploitables avec les ressources actuelles\",\n", "        f\"Évolution du marché peut rendre la segmentation obsolète\",\n", "        f\"Réglementation (RGPD) peut limiter l'utilisation de certaines données\",\n", "        f\"Coût d'acquisition vs valeur client à valider\",\n", "        f\"ROI des recommandations non quantifié\"\n", "    ],\n", "    '🔄 Évolutivité': [\n", "        f\"Nouveaux clients difficiles à classifier sans historique\",\n", "        f\"Changements saisonniers peuvent affecter les segments\",\n", "        f\"Croissance de l'entreprise peut modifier les profils\",\n", "        f\"Nouveaux produits/services peuvent créer de nouveaux segments\",\n", "        f\"Segmentation statique nécessitant des mises à jour régulières\"\n", "    ]\n", "}\n", "\n", "print(\"\\n=== ⚠️ LIMITES DE LA SEGMENTATION ===\")\n", "for category, limits in limitations.items():\n", "    print(f\"\\n{category} :\")\n", "    for i, limit in enumerate(limits, 1):\n", "        print(f\"   {i}. {limit}\")\n", "\n", "# Recommandations pour atténuer les limites\n", "mitigation_strategies = {\n", "    '🚀 Court terme (1-3 mois)': [\n", "        \"Valider les segments avec l'équipe métier\",\n", "        \"Tester les recommandations sur un échantillon\",\n", "        \"Collecter des feedbacks sur l'actionabilité\",\n", "        \"Mesurer l'impact des premières actions\",\n", "        \"Mettre en place le tracking des KPIs\"\n", "    ],\n", "    '📈 Moyen terme (3-6 mois)': [\n", "        \"Enrichir avec des données de satisfaction client\",\n", "        \"Intégrer des données comportementales web/app\",\n", "        \"Automatiser le scoring des nouveaux clients\",\n", "        \"Développer un dashboard de suivi des segments\",\n", "        \"Implémenter des tests A/B sur les recommandations\"\n", "    ],\n", "    '🎯 Long terme (6+ mois)': [\n", "        \"Mettre en place une collecte de données en temps réel\",\n", "        \"Développer des modèles prédictifs par segment\",\n", "        \"Intégrer l'IA pour la personnalisation\",\n", "        \"Créer un système de recommandations dynamiques\",\n", "        \"Implémenter une segmentation adaptative\"\n", "    ]\n", "}\n", "\n", "print(\"\\n=== 🎯 STRATÉGIES D'AMÉLIORATION ===\")\n", "for timeframe, strategies in mitigation_strategies.items():\n", "    print(f\"\\n{timeframe} :\")\n", "    for i, strategy in enumerate(strategies, 1):\n", "        print(f\"   {i}. {strategy}\")\n", "\n", "# Sauvegarde des limites et stratégies\n", "limitations_data = {\n", "    'limitations': limitations,\n", "    'mitigation_strategies': mitigation_strategies,\n", "    'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "    'current_segments': len(personas),\n", "    'update_frequency_recommended': update_freq\n", "}\n", "\n", "with open('reports/analysis/4_08_limitations_improvements.json', 'w') as f:\n", "    json.dump(limitations_data, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"\\n💾 Limites et améliorations sauvegardées : reports/analysis/4_08_limitations_improvements.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Préparation de la présentation\n", "\n", "### 6.1 Export des visuels pertinents pour le livrable PowerPoint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Génération et export des visuels pour présentation\n", "print(\"\\n🎨 Génération des visuels pour présentation...\")\n", "\n", "# Création du dossier pour les exports\n", "export_dir = 'reports/presentation_visuals'\n", "os.makedirs(export_dir, exist_ok=True)\n", "\n", "# Utilisation du module de visualisation optimisé\n", "presentation_visuals = export_presentation_visuals(\n", "    cluster_distribution=cluster_distribution,\n", "    cluster_means=cluster_means if not cluster_means.empty else None,\n", "    matrix_df=matrix_df if 'matrix_df' in locals() else None,\n", "    personas=personas,\n", "    recommendations=recommendations,\n", "    export_dir=export_dir,\n", "    key_vars=key_vars[:4] if len(key_vars) >= 4 else key_vars\n", ")\n", "\n", "print(f\"\\n✅ Visuels exportés dans {export_dir}/\")\n", "for visual in presentation_visuals:\n", "    print(f\"   - {visual}\")\n", "\n", "# Export supplémentaire : graphique de synthèse exécutive\n", "if not cluster_means.empty and len(key_vars) > 0:\n", "    fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "    # Graphique en barres des principales métriques\n", "    metrics_to_plot = key_vars[:3] if len(key_vars) >= 3 else key_vars\n", "    x_pos = np.arange(len(cluster_means))\n", "    width = 0.25\n", "\n", "    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']\n", "\n", "    for i, metric in enumerate(metrics_to_plot):\n", "        if metric in cluster_means.columns:\n", "            values = cluster_means[metric].values\n", "            ax.bar(x_pos + i*width, values, width,\n", "                  label=metric.replace('_', ' ').title(),\n", "                  color=colors[i % len(colors)], alpha=0.8)\n", "\n", "    ax.set_xlabel('Segments', fontsize=12, fontweight='bold')\n", "    ax.set_ylabel('Valeurs', fontsize=12, fontweight='bold')\n", "    ax.set_title('Synthèse Exécutive - Métriques Clés par Segment', fontsize=16, fontweight='bold')\n", "    ax.set_xticks(x_pos + width)\n", "    ax.set_xticklabels([f'Segment {i}' for i in cluster_means.index])\n", "    ax.legend()\n", "    ax.grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    plt.savefig(f'{export_dir}/00_synthese_executive.png', dpi=300, bbox_inches='tight')\n", "    plt.close()\n", "\n", "    print(f\"   - 00_synthese_executive.png (graphique de synthèse)\")\n", "\n", "print(f\"\\n📊 {len(presentation_visuals) + 1} visuels générés pour la présentation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.2 Tableaux de synthèse formatés"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création des tableaux finaux pour présentation\n", "print(\"\\n📋 Création des tableaux exécutifs pour présentation...\")\n", "\n", "# Tableau exécutif des segments\n", "executive_summary = []\n", "\n", "for cluster_id, persona in personas.items():\n", "    metrics = persona.get('metrics', {})\n", "    behavior = persona.get('comportement', {})\n", "    reco = recommendations.get(cluster_id, {})\n", "\n", "    executive_summary.append({\n", "        'Segment': f\"Segment {cluster_id}\",\n", "        'Nom': persona.get('nom', f'Segment {cluster_id}'),\n", "        'Taille': f\"{metrics.get('taille', 0):,} clients\",\n", "        'Pourcentage': f\"{metrics.get('pourcentage', 0):.1f}%\",\n", "        'Récence_moy': f\"{metrics.get('recency_mean', 0):.0f} jours\" if 'recency_mean' in metrics else 'N/A',\n", "        'Fréquence_moy': f\"{metrics.get('frequency_mean', 0):.1f}\" if 'frequency_mean' in metrics else 'N/A',\n", "        'Valeur_totale': f\"{metrics.get('monetary_total_mean', 0):.0f}€\" if 'monetary_total_mean' in metrics else 'N/A',\n", "        'Stratégie_clé': reco.get('strategies', ['N/A'])[0] if reco.get('strategies') else 'N/A',\n", "        'Priorité': reco.get('priority', 'Moyenne')\n", "    })\n", "\n", "exec_df = pd.DataFrame(executive_summary)\n", "\n", "# Tri par priorité\n", "priority_order = {'Critique': 1, 'Haute': 2, '<PERSON>ye<PERSON>': 3, 'Faible': 4}\n", "exec_df['Priority_rank'] = exec_df['Priorité'].map(priority_order)\n", "exec_df = exec_df.sort_values('Priority_rank').drop('Priority_rank', axis=1)\n", "\n", "print(\"\\n=== 📊 TABLEAU EXÉCUTIF DES SEGMENTS ===\")\n", "display(exec_df)\n", "\n", "# Export des tableaux\n", "exec_df.to_csv(f'{export_dir}/4_09_executive_summary.csv', index=False)\n", "if 'reco_df' in locals():\n", "    reco_df.to_csv(f'{export_dir}/4_09_marketing_recommendations.csv', index=False)\n", "\n", "# Création d'un fichier de synthèse marketing\n", "all_kpis = []\n", "for reco in recommendations.values():\n", "    if 'kpis' in reco:\n", "        all_kpis.extend(reco['kpis'][:2])\n", "\n", "marketing_synthesis = {\n", "    'date_analyse': datetime.now().strftime('%Y-%m-%d'),\n", "    'nombre_clients_analyses': len(df_clustered),\n", "    'nombre_segments': len(personas),\n", "    'segments_prioritaires': exec_df[exec_df['Priorité'].isin(['Critique', 'Haute'])]['Segment'].tolist(),\n", "    'actions_immediates': [\n", "        f\"Cibler le segment prioritaire : {exec_df.iloc[0]['Nom']} ({exec_df.iloc[0]['<PERSON>lle']})\",\n", "        f\"Implémenter la stratégie : {exec_df.iloc[0]['Stratégie_clé']}\",\n", "        f\"Surveiller les segments à risque d'attrition\",\n", "        f\"Développer les programmes de fidélisation pour les hautes valeurs\"\n", "    ],\n", "    'kpis_suivre': list(set(all_kpis)) if all_kpis else ['Taux de conversion', '<PERSON><PERSON> moyen', 'Rétention'],\n", "    'frequence_mise_a_jour': update_freq if 'update_freq' in locals() else 'Trimestrielle',\n", "    'budget_allocation': {\n", "        segment['Segment']: f\"{segment['Pourcentage']}\"\n", "        for segment in executive_summary\n", "    }\n", "}\n", "\n", "with open(f'{export_dir}/4_09_marketing_synthesis.json', 'w') as f:\n", "    json.dump(marketing_synthesis, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"\\n✅ Tableaux et synthèse exportés dans {export_dir}/ :\")\n", "print(f\"   - 4_09_executive_summary.csv\")\n", "print(f\"   - 4_09_marketing_recommendations.csv\")\n", "print(f\"   - 4_09_marketing_synthesis.json\")\n", "\n", "# Résumé final pour la présentation\n", "print(f\"\\n🎯 RÉSUMÉ EXÉCUTIF :\")\n", "print(f\"   📊 {len(df_clustered):,} clients analysés\")\n", "print(f\"   🎯 {len(personas)} segments identifiés\")\n", "print(f\"   🚀 {len([s for s in executive_summary if s['Priorité'] in ['Critique', 'Haute']])} segments prioritaires\")\n", "print(f\"   📈 {len(all_kpis)} KPIs de suivi recommandés\")\n", "print(f\"   🔄 Mise à jour recommandée : {marketing_synthesis['frequence_mise_a_jour']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "### ✅ Résumé des étapes réalisées\n", "\n", "- ✅ **Chargement des données** : Import des segments du Notebook 3 avec validation\n", "- ✅ **Analyse descriptive** : Statistiques détaillées par segment\n", "- ✅ **Visualisations comparatives** : Boxplots, radar charts, matrices de priorisation\n", "- ✅ **Création de personas** : Profils clients détaillés avec comportements\n", "- ✅ **Analyse comportementale** : Patterns temporels et de panier\n", "- ✅ **Recommandations marketing** : Stratégies personnalisées par segment\n", "- ✅ **Matrice de priorisation** : Classification en quadrants d'action\n", "- ✅ **Analyse de stabilité** : Évaluation temporelle et fréquence de mise à jour\n", "- ✅ **Identification des limites** : Points d'amélioration et stratégies d'évolution\n", "- ✅ **Export pour présentation** : Visuels et tableaux exécutifs\n", "\n", "### 🎯 Livrables générés\n", "\n", "**📊 Analyses :**\n", "- `4_01_descriptive_stats_by_cluster.csv` : Statistiques détaillées\n", "- `4_02_customer_personas.json` : Personas clients\n", "- `4_04_marketing_recommendations.json` : Recommandations complètes\n", "- `4_05_marketing_recommendations_summary.csv` : Synthèse des recommandations\n", "- `4_06_priority_matrix.csv` : Matrice de priorisation\n", "- `4_07_stability_analysis.csv` : Analyse de stabilité\n", "- `4_08_limitations_improvements.json` : Limites et améliorations\n", "\n", "**🎨 Visualisations :**\n", "- `4_01_cluster_distribution.png` : Répartition des segments\n", "- `4_02_segment_comparison.png` : Comparaison des métriques\n", "- `4_04_radar_charts.png` : Profils radar\n", "- `4_05_priority_matrix.png` : Matrice de priorisation\n", "- `4_06_stability_analysis.png` : Évolution temporelle\n", "\n", "**📋 Présentation :**\n", "- `4_09_executive_summary.csv` : Tableau exécutif\n", "- `4_09_marketing_synthesis.json` : Synthèse marketing\n", "- Visuels haute résolution pour présentation\n", "\n", "### 🚀 Recommandations prioritaires\n", "\n", "1. **🔥 <PERSON><PERSON><PERSON><PERSON><PERSON> (1 mois)** :\n", "   - C<PERSON>r les segments Champions avec programmes VIP\n", "   - Lancer campagnes de réactivation pour segments Dormants\n", "   - Mettre en place tracking des KPIs par segment\n", "\n", "2. **📈 Court terme (3 mois)** :\n", "   - Développer scoring automatique nouveaux clients\n", "   - Implémenter tests A/B sur recommandations\n", "   - Enrichir données comportementales\n", "\n", "3. **🎯 <PERSON><PERSON>n terme (6 mois)** :\n", "   - Segmentation dynamique temps réel\n", "   - <PERSON><PERSON><PERSON><PERSON> prédict<PERSON> de churn\n", "   - Dashboard de suivi des segments\n", "\n", "### 🔄 Prochaines étapes\n", "\n", "➡️ **Notebook 5 :** Simulation de maintenance et contrat de service  \n", "➡️ **Implémentation :** Tests des recommandations sur échantillons  \n", "➡️ **Suivi :** Mise en place des KPIs et tableaux de bord\n", "\n", "---\n", "\n", "**🎉 Recommandations marketing prêtes pour implémentation !**  \n", "**📊 Stratégies personnalisées par segment avec priorités d'action définies.**"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}