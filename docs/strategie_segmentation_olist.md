# Stratégie de Segmentation Client Olist

## 🚨 DIAGNOSTIC CRITIQUE

### Découverte Majeure
**99,441 commandes = 99,441 clients uniques** → **CHAQUE CLIENT N'A QU'UNE SEULE COMMANDE**

Cette réalité change complètement l'approche de segmentation :
- ❌ RFM classique impossible (Frequency = 1 pour tous)
- ❌ Variables de variabilité inutiles (pas de variation par client)
- ❌ Analyse de fidélisation inadaptée (pas de clients récurrents)

### Problèmes de Qualité des Données
1. **2,965 commandes sans date de livraison** (3% des données)
2. **1,234 commandes non valides** (625 annulées + 609 indisponibles)
3. **Variables redondantes** : 22 variables dont beaucoup de doublons
4. **Nettoyage insuffisant** : Pas de filtrage des données problématiques

## 🎯 NOUVELLE APPROCHE : SEGMENTATION "FIRST PURCHASE"

### Principe
Puisque tous les clients n'ont qu'un achat, segmenter sur :
- **Récence** : Quand ont-ils acheté ?
- **Montant** : Combien ont-ils dépensé ?
- **Contexte** : Où, quand, comment ?

### Variables Pertinentes (6 maximum)
1. **`recency_days`** : Jours depuis l'achat
2. **`order_value`** : Montant de la commande
3. **`customer_state`** : Localisation géographique
4. **`purchase_month`** : Saisonnalité
5. **`delivery_days`** : Délai de livraison
6. **`review_score`** : Satisfaction client

### Variables à ÉLIMINER
- `frequency` (toujours = 1)
- `customer_lifespan_days` (toujours = 0)
- `amount_std`, `amount_cv` (toujours = 0)
- Toutes les variables de variabilité

## 📋 PLAN D'ACTION (5 JOURS)

### JOUR 1 : Nettoyage Fondamental (Notebook 1)

#### Matin : Diagnostic des Données
```python
# 1. Analyser les statuts de commandes
status_counts = orders['order_status'].value_counts()
invalid_orders = orders[orders['order_status'].isin(['canceled', 'unavailable'])]

# 2. Quantifier les données manquantes
missing_delivery = orders['order_delivered_customer_date'].isnull().sum()

# 3. Confirmer le pattern "1 client = 1 commande"
orders_per_customer = orders.groupby('customer_id').size()
print(f"Clients avec >1 commande : {(orders_per_customer > 1).sum()}")
```

#### Après-midi : Nettoyage
```python
# Filtrer les commandes valides et livrées
clean_orders = orders[
    (orders['order_status'] == 'delivered') & 
    (orders['order_delivered_customer_date'].notna())
]
print(f"Dataset nettoyé : {len(clean_orders)} commandes valides")
```

### JOUR 2 : Feature Engineering Adapté (Notebook 2)

#### Variables Réalistes
```python
def create_first_purchase_features(df):
    # Récence
    reference_date = df['order_purchase_timestamp'].max()
    df['recency_days'] = (reference_date - df['order_purchase_timestamp']).dt.days
    
    # Montant par commande
    df['order_value'] = df.groupby('order_id')['price'].transform('sum')
    
    # Géographie
    df['state_encoded'] = LabelEncoder().fit_transform(df['customer_state'])
    
    # Temporalité
    df['purchase_month'] = df['order_purchase_timestamp'].dt.month
    
    # Performance logistique
    df['delivery_days'] = (df['order_delivered_customer_date'] - 
                          df['order_purchase_timestamp']).dt.days
    
    return df[['customer_id', 'recency_days', 'order_value', 
              'state_encoded', 'purchase_month', 'delivery_days']]
```

### JOUR 3 : Clustering Optimisé (Notebook 3)

#### Segmentation Adaptée
```python
# Variables pour clustering (6 max)
features = ['recency_days', 'order_value', 'state_encoded', 
           'purchase_month', 'delivery_days', 'review_score']

# Clustering K-means
kmeans = KMeans(n_clusters=5, random_state=42)
clusters = kmeans.fit_predict(X_scaled[features])
```

#### Segments Attendus
- **Nouveaux Premium** : Récents + montant élevé
- **Clients Régionaux** : Concentration géographique
- **Acheteurs Saisonniers** : Patterns temporels
- **Value Seekers** : Montant faible
- **Express Shoppers** : Livraison rapide

### JOUR 4 : Analyses Marketing (Notebook 4)

#### Personas Adaptés
```python
# Profils "First Purchase"
personas = {
    'Premium_Newcomers': 'Nouveaux clients à fort potentiel',
    'Regional_Shoppers': 'Clients concentrés géographiquement',
    'Seasonal_Buyers': 'Achats liés aux périodes',
    'Price_Conscious': 'Sensibles au prix',
    'Fast_Delivery': 'Priorité à la rapidité'
}
```

#### Recommandations Business
- **Acquisition** : Cibler les non-clients similaires
- **Réactivation** : Relancer pour générer le 2e achat
- **Géo-marketing** : Stratégies par région
- **Saisonnalité** : Campagnes selon les patterns

### JOUR 5 : Maintenance et Livrables (Notebook 5)

#### Fréquence de Mise à Jour
- **Mensuelle** : Intégrer nouveaux clients
- **Trimestrielle** : Analyser évolution saisonnière
- **Annuelle** : Revoir la stratégie globale

## 🎯 OBJECTIFS RÉVISÉS

### Techniques
- **4-6 segments** équilibrés et distincts
- **Score de silhouette > 0.4** (réaliste pour ce contexte)
- **6 variables maximum** non redondantes

### Business
- **Stratégies d'acquisition** ciblées
- **Campagnes de réactivation** pour 2e achat
- **Géo-marketing** par région/état
- **Exploitation de la saisonnalité**

### Livrables
- **Dataset nettoyé** (commandes valides uniquement)
- **Modèle de segmentation** adapté au "first purchase"
- **5 personas clients** exploitables
- **Recommandations marketing** par segment
- **Plan de maintenance** avec fréquences

## ✅ CRITÈRES DE SUCCÈS

### Qualité des Données
- [ ] 0% de variables à variance nulle
- [ ] < 2% de données manquantes
- [ ] Variables non corrélées (r < 0.7)
- [ ] Filtrage des commandes non valides

### Qualité du Clustering
- [ ] Score de silhouette > 0.4
- [ ] Segments équilibrés (min 10% des données)
- [ ] Interprétation business claire
- [ ] Cohérence avec le contexte "first purchase"

### Impact Business
- [ ] Recommandations actionnables par segment
- [ ] Stratégies d'acquisition définies
- [ ] Plan de réactivation structuré
- [ ] ROI estimé et justifié

---

**Cette stratégie unique transforme un projet inadapté en solution exploitable en reconnaissant la vraie nature des données : une plateforme d'acquisition de nouveaux clients plutôt qu'un système de fidélisation.**
